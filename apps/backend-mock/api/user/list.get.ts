import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

// 生成模拟数据
function generateMockUsers(count: number) {
  const users = [];
  for (let i = 0; i < count; i++) {
    users.push({
      id: faker.string.uuid(),
      name: faker.person.fullName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
      createTime: faker.date.past(),
      status: faker.helpers.arrayElement([0, 1]),
    });
  }
  return users;
}

// 处理请求
export default defineEventHandler(async (event) => {
  // 验证token
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  // 获取查询参数
  const query = getQuery(event);
  const page = Number(query.page) || 1;
  const pageSize = Number(query.pageSize) || 10;

  // 生成数据
  const total = 100;
  const list = generateMockUsers(pageSize);

  // 返回成功响应
  return useResponseSuccess({
    items: list,
    total,
    page,
    pageSize,
  });
});