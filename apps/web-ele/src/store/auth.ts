import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { LOGIN_PATH } from '@vben/constants';
import { useAppConfig } from '@vben/hooks';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { ElNotification } from 'element-plus';
import { defineStore } from 'pinia';

import { getUserInfoApi, loginApi, logoutApi } from '#/api';

// OAuth state key（保留 state 存取，不使用 PKCE）
const PKCE_STATE_KEY = 'pkce_state';

export const useAuthStore = defineStore('auth', () => {
  const { apiURL, oauthServerUrl, base, sessionKey, clientId } = useAppConfig(
    import.meta.env,
    import.meta.env.PROD,
  );
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(params: Recordable<any>) {
    console.warn('authLogin 被调用，参数:', params);
    // 异步处理用户登录操作并获取 accessToken
    const userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;

      // 1. 表单登录，获取会话令牌（luc-auth-token）
      const sessionToken = await loginApi(params);
      console.warn('1、获取到sessionToken：', sessionToken);
      // 如果成功获取到会话令牌
      if (sessionToken) {
        // 2. 仅保留 state，走标准授权码 + 客户端凭证
        const redirectUrl = `${location.origin}${base}/oauth2/callback`;
        const state = Date.now().toString(36);
        const scope = import.meta.env.VITE_OAUTH_SCOPE || 'read';
        // 存储session凭证
        sessionStorage.setItem(sessionKey, sessionToken);
        // 存储 state 以便回调校验
        sessionStorage.setItem(PKCE_STATE_KEY, state);
        // 构造授权请求（不带 PKCE 参数）
        window.location.replace(
          `${apiURL}${oauthServerUrl}/oauth2/authorize?response_type=code` +
            `&client_id=${encodeURIComponent(clientId)}` +
            `&redirect_uri=${encodeURIComponent(redirectUrl)}` +
            `&state=${encodeURIComponent(state)}` +
            `&scope=${encodeURIComponent(scope)}`,
        );
        return;
      } else {
        throw new Error('登录失败，未获取到会话令牌');
      }
    } catch (error) {
      console.error('登录失败:', error);
      ElNotification({
        message: error instanceof Error ? error.message : '登录失败',
        title: '登录失败',
        type: 'error',
      });
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  /**
   * oauth2登陆
   * @param providerId oauth2提供者id
   */
  async function oauth2Login(providerId: string) {
    try {
      loginLoading.value = true;
      // oauth2LoginApi(providerId);
      // window.location.href = data;
      window.location.href = `/api/auth-server/oauth2/authorization/${providerId}`;
    } finally {
      loginLoading.value = false;
    }
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo() {
    try {
      let userInfo: null | UserInfo = null;
      userInfo = await getUserInfoApi();
      userStore.setUserInfo(userInfo);
      return userInfo;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 清除无效的 token
      accessStore.setAccessToken(null);
      throw error;
    }
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
    oauth2Login,
  };
});
