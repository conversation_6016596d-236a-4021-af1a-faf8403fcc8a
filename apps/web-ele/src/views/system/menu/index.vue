<!-- eslint-disable perfectionist/sort-imports -->
<script lang="tsx" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import { ref } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  ElAlert,
  ElButton,
  ElDescriptions,
  ElDescriptionsItem,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
} from 'element-plus';
import { getMenuList } from '#/api';
import type { MenuApi } from '#/api';
import { $t } from '@vben/locales';

defineOptions({
  name: 'MenuManage',
});

const MOCK_DATA = ref<MenuApi.MenuInfoVO[]>([]);

// 编辑模态框API
const [editModal, editModalApi] = useVbenModal({
  title: '编辑菜单',
  onCancel() {
    editModalApi.close();
  },
  async onConfirm() {
    // 直接从共享数据中获取表单值
    const { formData } = editModalApi.getData<{
      formData: MenuApi.MenuInfoVO;
      originalRow: MenuApi.MenuInfoVO;
    }>();
    if (formData) {
      try {
        // 锁定模态框，防止重复提交
        editModalApi.lock();
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));
        // 更新本地数据（实际应用中应该是API调用）
        const index = MOCK_DATA.value.findIndex(
          (item) => item.id === formData.id,
        );
        if (index !== -1) {
          MOCK_DATA.value[index] = { ...formData };
        }
        ElMessage.success('保存成功');
        editModalApi.close();
      } catch (error) {
        ElMessage.error('保存失败');
        console.error(error);
      } finally {
        // 解锁模态框
        editModalApi.unlock();
      }
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 当模态框打开时，共享数据已经通过setData设置好了
      // 不需要额外处理
    }
  },
});

// 删除模态框API
const [deleteModal, deleteModalApi] = useVbenModal({
  title: '删除确认',
  onCancel() {
    deleteModalApi.close();
  },
  async onConfirm() {
    try {
      // 锁定模态框
      deleteModalApi.lock();
      // 获取要删除的行数据
      const { row } = deleteModalApi.getData<{ row: MenuApi.MenuInfoVO }>();
      if (row) {
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));
        // 从本地数据中删除（实际应用中应该是API调用）
        const index = MOCK_DATA.value.findIndex((item) => item.id === row.id);
        if (index !== -1) {
          MOCK_DATA.value.splice(index, 1);
        }
        ElMessage.success('删除成功');
        deleteModalApi.close();
      }
    } catch (error) {
      ElMessage.error('删除失败');
      console.error(error);
    } finally {
      // 解锁模态框
      deleteModalApi.unlock();
    }
  },
});

// 查看模态框API
const [viewModal, viewModalApi] = useVbenModal({
  title: '查看菜单详情',
  showConfirmButton: false,
  onCancel() {
    viewModalApi.close();
  },
});

// 新增菜单模态框API
const [createModal, createModalApi] = useVbenModal({
  title: '新增菜单',
  onCancel() {
    createModalApi.close();
  },
  async onConfirm() {
    try {
      // 获取表单数据
      const { formData } = createModalApi.getData<{
        formData: Partial<MenuApi.MenuInfoVO>;
      }>();
      if (formData && formData.name) {
        // 锁定模态框
        createModalApi.lock();
        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));
        // 创建新菜单项
        const newItem: MenuApi.MenuInfoVO = formData as MenuApi.MenuInfoVO;
        // 添加到数据列表
        MOCK_DATA.value.push(newItem);
        ElMessage.success('创建成功');
        createModalApi.close();
      } else {
        ElMessage.warning('请填写菜单名称');
      }
    } catch (error) {
      ElMessage.error('创建失败');
      console.error(error);
    } finally {
      // 解锁模态框
      createModalApi.unlock();
    }
  },
});

// 打开编辑模态框
function openEditModal(row: MenuApi.MenuInfoVO) {
  // 创建一个副本以便编辑，避免直接修改原始数据
  const formData = { ...row };
  // 使用setData方法设置共享数据
  editModalApi
    .setData({
      // 用于编辑的表单数据（副本）
      formData,
      // 原始行数据（用于比较或恢复）
      originalRow: row,
    })
    .open();
}

// 打开删除确认模态框
function openDeleteModal(row: MenuApi.MenuInfoVO) {
  deleteModalApi.setData({ row }).open();
}

// 打开查看详情模态框
function openViewModal(row: MenuApi.MenuInfoVO) {
  viewModalApi.setData({ row }).open();
}

// 打开新增菜单模态框
function openCreateModal() {
  // 初始化一个空的表单数据
  createModalApi
    .setData({
      formData: {
        name: '',
      },
    })
    .open();
}

// 表格格式配置
const gridOptions: VxeGridProps<MenuApi.MenuInfoVO> = {
  columns: [
    { field: 'meta.title', title: `${$t('form.menu.header.title')}` },
    { field: 'name', title: `${$t('form.menu.header.name')}` },
    { field: 'path', title: `${$t('form.menu.header.path')}` },
    { field: 'component', title: `${$t('form.menu.header.component')}` },
    { field: 'menuType', title: `${$t('form.menu.header.menuType')}` },
    { field: 'status', title: `${$t('form.menu.header.status')}` },
    { field: 'dtCreated', title: `${$t('form.menu.header.dtCreated')}` },
    {
      field: 'operation',
      fixed: 'right',
      title: `${$t('form.menu.header.operation.title')}`,
      width: 200,
      cellRender: {
        name: 'CellVbenButtonGroup',
        props: { gap: 10 },
        options: [
          {
            text: `${$t('form.menu.header.operation.edit')}`,
            variant: 'primary',
            handler: (row: MenuApi.MenuInfoVO) => {
              openEditModal(row);
            },
          },
          {
            text: `${$t('form.menu.header.operation.delete')}`,
            variant: 'destructive',
            handler: (row: MenuApi.MenuInfoVO) => {
              openDeleteModal(row);
            },
          },
        ],
      },
    },
  ],
  rowConfig: { keyField: 'id' },
  proxyConfig: {
    ajax: {
      query: async () => {
        const menus = await getMenuList();
        console.log('加载数据：', menus);
        return menus;
      },
    },
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  treeConfig: {
    parentField: 'parentMenuId',
    rowField: 'menuId',
    transform: false,
  },
};
// 表格点击事件配置
const gridEvents: VxeGridListeners<MenuApi.MenuInfoVO> = {
  cellClick: ({ row, column }) => {
    if (column.title === 'ID') {
      openViewModal(row);
    }
  },
};

const [Grid] = useVbenVxeGrid({ gridOptions, gridEvents });
</script>
<template>
  <Page description="page.system.menu" title="菜单管理">
    <div class="mb-4">
      <ElButton type="primary" @click="openCreateModal">新增菜单</ElButton>
    </div>
    <Grid />
    <!-- 编辑模态框 -->
    <editModal>
      <template #default>
        <ElForm label-width="100px">
          <ElFormItem label="菜单名称">
            <ElInput v-model="editModalApi.getData().formData.name" />
          </ElFormItem>
          <ElFormItem label="创建日期">
            <ElInput
              v-model="editModalApi.getData().formData.createDt"
              disabled
            />
          </ElFormItem>
        </ElForm>
      </template>
    </editModal>
    <!-- 删除模态框 -->
    <deleteModal>
      <template #default>
        <ElAlert type="warning" :closable="false" show-icon>
          <p>
            确定要删除菜单
            <strong>{{ deleteModalApi.getData()?.row?.name }}</strong> 吗？
          </p>
          <p>此操作不可恢复！</p>
        </ElAlert>
      </template>
    </deleteModal>
    <!-- 查看模态框 -->
    <viewModal>
      <template #default>
        <ElDescriptions :column="1" border>
          <ElDescriptionsItem label="菜单名称">
            {{ viewModalApi.getData()?.row?.name }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="ID">
            {{ viewModalApi.getData()?.row?.id }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="创建日期">
            {{ viewModalApi.getData()?.row?.createDt }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </template>
    </viewModal>
    <!-- 创建模态框 -->
    <createModal>
      <template #default>
        <ElForm label-width="100px">
          <ElFormItem label="菜单名称">
            <ElInput v-model="createModalApi.getData().formData.name" />
          </ElFormItem>
        </ElForm>
      </template>
    </createModal>
  </Page>
</template>
