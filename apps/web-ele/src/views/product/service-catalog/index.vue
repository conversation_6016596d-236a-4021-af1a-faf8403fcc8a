<script lang="tsx" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElSelect,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

defineOptions({
  name: 'ServiceCatalog',
});

interface ServiceItem {
  id: string;
  serviceName: string;
  serviceType: string;
  description: string;
  price: number;
  status: string;
  createTime: Date;
}

const MOCK_DATA = ref(
  Array.from({ length: 10 }).map<ServiceItem>((_, index) =>
    dataGenerator(index),
  ),
);

// 服务类型选项
const serviceTypeOptions = [
  { label: '计算服务', value: '计算服务' },
  { label: '存储服务', value: '存储服务' },
  { label: '网络服务', value: '网络服务' },
  { label: '数据库服务', value: '数据库服务' },
  { label: '安全服务', value: '安全服务' },
];

// 创建新增模态框API
const [createModal, createModalApi] = useVbenModal({
  title: '新增服务',
  onCancel() {
    createModalApi.close();
  },
  async onConfirm() {
    try {
      const { formData } = createModalApi.getData<{
        formData: Partial<ServiceItem>;
      }>();
      if (formData && formData.serviceName) {
        createModalApi.lock();
        await new Promise((resolve) => setTimeout(resolve, 1000));
        const newItem: ServiceItem = {
          id: `service-${MOCK_DATA.value.length + 1}`,
          serviceName: formData.serviceName,
          serviceType: formData.serviceType || '计算服务',
          description: formData.description || '',
          price: formData.price || 0,
          status: formData.status || '启用',
          createTime: new Date(),
        };
        MOCK_DATA.value.push(newItem);
        ElMessage.success('创建成功');
        createModalApi.close();
      } else {
        ElMessage.warning('请填写服务名称');
      }
    } catch (error) {
      ElMessage.error('创建失败');
      console.error(error);
    } finally {
      createModalApi.unlock();
    }
  },
});

// 打开新增模态框
function openCreateModal() {
  createModalApi
    .setData({
      formData: {
        serviceName: '',
        serviceType: '计算服务',
        description: '',
        price: 0,
        status: '启用',
      },
    })
    .open();
}

// 表格格式配置
const gridOptions: VxeGridProps<ServiceItem> = {
  columns: [
    { field: 'id', title: 'ID', type: 'seq', width: 50 },
    { field: 'serviceName', title: '服务名称', width: 200 },
    { field: 'serviceType', title: '服务类型', width: 120 },
    { field: 'description', title: '服务描述', minWidth: 200 },
    { field: 'price', title: '价格(元/小时)', width: 120 },
    {
      field: 'status',
      title: '状态',
      width: 80,
      cellRender: {
        name: 'CellTag',
        props: ({ row }: { row: ServiceItem }) => ({
          color: row.status === '启用' ? 'success' : 'error',
          text: row.status,
        }),
      },
    },
    { field: 'createTime', title: '创建时间', width: 180 },
  ],
  data: MOCK_DATA.value,
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
};

const [Grid] = useVbenVxeGrid({ gridOptions });

function dataGenerator(index: number): ServiceItem {
  const types = ['计算服务', '存储服务', '网络服务', '数据库服务', '安全服务'];
  const names = [
    '云服务器',
    '对象存储',
    '负载均衡',
    'MySQL数据库',
    'Web应用防火墙',
  ];
  const descriptions = [
    '弹性云服务器，提供安全可靠的弹性计算服务',
    '海量、安全、低成本、高可靠的云存储服务',
    '对多台云服务器进行流量分发的负载均衡服务',
    '高性能、高可靠的MySQL云数据库服务',
    '基于云安全大数据能力的Web应用安全防护',
  ];

  return {
    id: `service-${index + 1}`,
    serviceName: `${names[index % names.length]} ${index + 1}`,
    serviceType: types[index % types.length]!,
    description: descriptions[index % descriptions.length]!,
    price: Number((Math.random() * 10).toFixed(2)),
    status: index % 3 === 0 ? '禁用' : '启用',
    createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
  };
}
</script>

<template>
  <Page description="管理系统中的所有服务目录" title="服务目录管理">
    <div class="mb-4">
      <ElButton type="primary" @click="openCreateModal">新增服务</ElButton>
    </div>
    <Grid />

    <!-- 创建模态框 -->
    <createModal>
      <template #default>
        <ElForm label-width="100px">
          <ElFormItem label="服务名称">
            <ElInput v-model="createModalApi.getData().formData.serviceName" />
          </ElFormItem>
          <ElFormItem label="服务类型">
            <ElSelect v-model="createModalApi.getData().formData.serviceType">
              <ElOption
                v-for="option in serviceTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="服务描述">
            <ElInput
              v-model="createModalApi.getData().formData.description"
              type="textarea"
              :rows="3"
            />
          </ElFormItem>
          <ElFormItem label="价格(元/小时)">
            <ElInputNumber
              v-model="createModalApi.getData().formData.price"
              :min="0"
              :precision="2"
            />
          </ElFormItem>
          <ElFormItem label="状态">
            <ElRadioGroup v-model="createModalApi.getData().formData.status">
              <ElRadio value="启用">启用</ElRadio>
              <ElRadio value="禁用">禁用</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
        </ElForm>
      </template>
    </createModal>
  </Page>
</template>
