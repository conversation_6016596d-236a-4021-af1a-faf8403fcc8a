import type { UserInfo } from '@vben/types';

import type { UserQuotaInfoVO } from './quota';

import { lucSystemPrefix, requestClient } from '#/api/request';

export type UserDetail = {
  /**
   * 租户配额信息
   */
  quotas: Array<UserQuotaInfoVO>;
  /**
   * 租户信息
   */
  userInfo: UserInfo;
};

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<UserInfo>(`${lucSystemPrefix}/user/info`);
}

export async function getUserList() {
  return requestClient.get<UserInfo[]>(`${lucSystemPrefix}/user/list`);
}
