// import { useAppConfig } from '@vben/hooks';

import {
  authServerPrefix,
  baseRequestClient,
  requestClient,
} from '#/api/request';

// const { tokenHeader } = useAppConfig(import.meta.env, import.meta.env.PROD);
export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
  }

  export interface OAuth2AccessTokenParams {
    grant_type: string; // authorization_code
    code: string; // 授权码
    redirect_uri: string; // 回调地址，需与授权时一致
    client_id?: string; // 兼容传入，但使用 Basic 认证更常见
    client_secret?: string;
  }

  export interface OAuth2AccessTokenResult {
    access_token: string;
    expires_in: number;
    tokey_type: string;
    scope: string;
    refresh_token: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<string>(`${authServerPrefix}/login`, data, true, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Accept: 'application/json',
    },
    responseReturn: 'body',
  });
}

export async function getAuthorizationCodeApi(params: {
  client_id?: string;
  redirectUrl: string;
  state?: string;
}) {
  const { redirectUrl, client_id = 'vben-ele-client', state } = params;
  return requestClient.get<string>(`${authServerPrefix}/oauth2/authorize`, {
    params: {
      response_type: 'code',
      client_id,
      redirectUrl,
      state,
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 使用授权码获取 AccessToken
 */
export async function getAccessTokenApi(
  params: AuthApi.OAuth2AccessTokenParams,
) {
  const basic = btoa(`${params.client_id}:${params.client_secret}`);
  return requestClient.post<AuthApi.OAuth2AccessTokenResult>(
    `${authServerPrefix}/oauth2/token`,
    params,
    false,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${basic}`,
        Accept: 'application/json',
      },
      responseReturn: 'body',
      withAuthorization: false,
    },
  );
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>(
    `${authServerPrefix}/auth-server/refresh`,
    {
      withCredentials: true,
    },
  );
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post(`${authServerPrefix}/logout`, {
    withCredentials: true,
  });
}

/**
 * 发送短信验证码
 */
export async function sendSmsCodeApi(phone: string) {
  return requestClient.post(`${authServerPrefix}/sms/code`, { phone }, true);
}

/**
 * 短信验证码登录
 */
export async function smsLoginApi(phoneNumber: string, code: string) {
  return requestClient.post<AuthApi.LoginResult>(
    `${authServerPrefix}/sms/login`,
    {
      phoneNumber,
      code,
    },
  );
}
