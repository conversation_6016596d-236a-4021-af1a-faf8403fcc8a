# 动态路由功能使用指南

## 概述

本项目已成功集成了基于 vben-admin 的动态路由功能，支持前端、后端和混合三种权限控制模式。

## 🚀 主要功能

### 1. 动态路由生成

- 支持从后端 API 获取菜单数据
- 自动转换为 Vue Router 路由配置
- 支持组件懒加载和路径映射

### 2. 权限控制

- **角色权限**：基于用户角色的访问控制
- **权限码**：基于具体权限码的精细化控制
- **vben 风格**：支持 `authority` 属性的权限控制

### 3. 多种权限模式

- `frontend`：前端权限控制
- `backend`：后端权限控制
- `mixed`：混合模式

## 📝 使用方法

### 1. 权限控制组件

```vue
<template>
  <!-- 基于角色的权限控制 -->
  <AccessControl :codes="['admin']" type="role">
    <el-button type="primary">管理员功能</el-button>
  </AccessControl>

  <!-- 基于权限码的权限控制 -->
  <AccessControl :codes="['user:create', 'user:update']" type="code">
    <el-button type="success">用户操作</el-button>
  </AccessControl>

  <!-- 多个权限码（满足其中一个即可） -->
  <AccessControl :codes="['system:read', 'system:write']" type="code">
    <div>系统管理内容</div>
  </AccessControl>
</template>
```

### 2. 权限指令

```vue
<template>
  <!-- 角色权限指令 -->
  <el-button v-access:role="['admin']" type="danger">
    删除操作（仅管理员可见）
  </el-button>

  <!-- 权限码指令 -->
  <el-button v-access:code="['user:delete']" type="warning">
    删除用户
  </el-button>

  <!-- 多个权限 -->
  <div v-access:role="['admin', 'manager']">管理员或经理可见的内容</div>
</template>
```

### 3. Composable 使用

```typescript
import { useAccess } from '@/composables/useAccess';

export default {
  setup() {
    const {
      hasAccessByRoles,
      hasAccessByCodes,
      hasAccessByRoute,
      userRoles,
      userCodes,
    } = useAccess();

    // 检查角色权限
    const canManage = hasAccessByRoles(['admin', 'manager']);

    // 检查权限码
    const canCreateUser = hasAccessByCodes(['user:create']);

    // 检查路由权限
    const canAccessRoute = hasAccessByRoute({
      roles: ['admin'],
      permissions: ['system:read'],
    });

    return {
      canManage,
      canCreateUser,
      canAccessRoute,
      userRoles,
      userCodes,
    };
  },
};
```

## 🔧 配置说明

### 1. 权限模式配置

在 `src/config/preferences.ts` 中配置权限模式：

```typescript
export const preferences: AppPreferences = {
  accessMode: 'backend', // 'frontend' | 'backend' | 'mixed'
  // ... 其他配置
};
```

### 2. 菜单数据格式

后端返回的菜单数据应符合以下格式：

```typescript
{
  name: 'Dashboard',
  path: '/dashboard',
  component: '/dashboard/analytics', // 组件路径
  redirect: '/dashboard/analytics',
  meta: {
    title: '仪表板',
    icon: 'lucide:layout-dashboard',
    order: 1,
    authority: ['admin'], // vben 风格权限
    hidden: false
  },
  children: [
    {
      name: 'Analytics',
      path: '/dashboard/analytics',
      component: '/dashboard/analytics',
      meta: {
        title: '分析页',
        icon: 'lucide:bar-chart-3'
      }
    }
  ]
}
```

### 3. 组件路径映射

系统会自动将菜单中的组件路径映射到实际的 Vue 文件：

- `/dashboard/analytics` → `src/views/dashboard/analytics.vue`
- `/system/user/index` → `src/views/system/user/index.vue`
- `/profile/index` → `src/views/profile/index.vue`

## 🎯 最佳实践

### 1. 权限设计

- 使用角色进行粗粒度控制
- 使用权限码进行细粒度控制
- 合理设计权限层级结构

### 2. 组件组织

- 按功能模块组织 views 目录
- 保持组件路径与菜单路径的一致性
- 使用 index.vue 作为模块入口文件

### 3. 性能优化

- 利用组件懒加载减少初始包大小
- 合理使用权限缓存
- 避免过度的权限检查

## 🐛 故障排除

### 1. 组件找不到

如果出现 "route component is invalid" 错误：

- 检查菜单数据中的 component 路径是否正确
- 确认对应的 Vue 文件是否存在
- 查看控制台输出的可用组件列表

### 2. 权限不生效

- 检查用户角色和权限码是否正确设置
- 确认权限模式配置是否正确
- 验证路由守卫是否正常工作

### 3. 路由重复

- 确保路由名称唯一
- 检查是否有重复的路由路径
- 清理浏览器缓存重新测试

## 📚 相关文件

- `src/utils/access.ts` - 核心动态路由逻辑
- `src/composables/useAccess.ts` - 权限控制 Composable
- `src/components/AccessControl.vue` - 权限控制组件
- `src/directives/access.ts` - 权限指令
- `src/config/preferences.ts` - 应用配置
- `src/api/menu.ts` - 菜单 API 接口
