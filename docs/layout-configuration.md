# 布局配置指南

## 侧边栏顶部图标和名称配置

### 📍 配置位置

侧边栏顶部的图标和名称可以通过以下两种方式配置：

#### 方式一：修改配置文件（推荐）

在 `src/config/app.ts` 文件中修改：

```typescript
export const defaultAppConfig: AppConfig = {
  app: {
    name: 'Luc System', // 👈 修改这里的应用名称
    title: 'Luc 管理系统',
    // ... 其他配置
  },

  logo: {
    enable: true, // 是否显示 Logo
    source: '/logo.png', // 👈 修改这里的 Logo 路径
    width: 32,
    height: 32,
    alt: 'Luc System Logo',
  },

  // ... 其他配置
};
```

#### 方式二：直接修改布局文件

在 `src/layouts/BasicLayout.vue` 文件中找到 `appConfig` 的使用位置：

```vue
<template>
  <!-- Logo 区域 -->
  <div class="vben-layout-sidebar__logo">
    <div class="vben-logo">
      <img
        v-if="appConfig.logo.enable"
        :src="appConfig.logo.source"     <!-- 👈 Logo 图片路径 -->
        :alt="appConfig.app.name"
        class="vben-logo__image"
      />
      <h1
        v-if="!appStore.sidebarCollapsed"
        class="vben-logo__title"
      >
        {{ appConfig.app.name }}          <!-- 👈 应用名称 -->
      </h1>
    </div>
  </div>
</template>
```

### 🎨 配置选项详解

#### Logo 配置

```typescript
logo: {
  enable: boolean,        // 是否显示 Logo
  source: string,         // Logo 图片路径（相对于 public 目录）
  width?: number,         // Logo 宽度（像素）
  height?: number,        // Logo 高度（像素）
  alt?: string,          // 图片 alt 属性
}
```

#### 应用信息配置

```typescript
app: {
  name: string,           // 应用名称（显示在侧边栏）
  title: string,          // 应用标题（用于页面标题）
  description: string,    // 应用描述
  version: string,        // 版本号
  author: string,         // 作者
  // ... 其他配置
}
```

### 📁 Logo 文件放置

1. **推荐方式**：将 Logo 文件放在 `public` 目录下

   ```
   public/
   ├── logo.png           # 主 Logo
   ├── logo-dark.png      # 暗色主题 Logo（可选）
   └── favicon.ico        # 网站图标
   ```

2. **配置路径**：
   ```typescript
   logo: {
     source: '/logo.png',        // 对应 public/logo.png
     // 或者
     source: '/assets/logo.png', // 对应 public/assets/logo.png
   }
   ```

### 🎯 常见配置示例

#### 示例 1：基础配置

```typescript
{
  app: {
    name: '我的管理系统',
  },
  logo: {
    enable: true,
    source: '/logo.png',
  }
}
```

#### 示例 2：不显示 Logo，只显示文字

```typescript
{
  app: {
    name: 'Admin System',
  },
  logo: {
    enable: false,  // 不显示 Logo
  }
}
```

#### 示例 3：自定义 Logo 尺寸

```typescript
{
  app: {
    name: 'Enterprise System',
  },
  logo: {
    enable: true,
    source: '/custom-logo.svg',
    width: 40,
    height: 40,
  }
}
```

### 🔧 高级配置

#### 响应式显示

Logo 和名称的显示会根据侧边栏的展开/收起状态自动调整：

- **展开状态**：显示 Logo + 名称
- **收起状态**：只显示 Logo

#### 主题适配

可以根据主题模式配置不同的 Logo：

```typescript
// 在组件中动态切换
const logoSource = computed(() => {
  return isDarkMode.value
    ? appConfig.logo.darkSource || appConfig.logo.source
    : appConfig.logo.source;
});
```

### 📝 注意事项

1. **图片格式**：推荐使用 PNG、SVG 格式
2. **图片尺寸**：建议 32x32 或 40x40 像素
3. **文件大小**：控制在 50KB 以内
4. **命名规范**：使用小写字母和连字符

### 🚀 快速修改

如果你只想快速修改名称和 Logo，可以直接编辑：

1. 替换 `public/logo.png` 文件
2. 修改 `src/config/app.ts` 中的 `app.name` 字段
3. 重启开发服务器查看效果

就这么简单！🎉
