<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.lc</groupId>
        <artifactId>luc-system</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>system-boot</artifactId>

    <dependencies>
        <!--rest规范及工具类-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-core</artifactId>
        </dependency>
        <!--web依赖-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-web</artifactId>
        </dependency>

        <!--datascope-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-datascope</artifactId>
        </dependency>

        <!--datasource-->
<!--        <dependency>-->
<!--            <groupId>com.lc</groupId>-->
<!--            <artifactId>framework-datasource-starter</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->

        <!--redis-->
<!--        <dependency>-->
<!--            <groupId>com.lc</groupId>-->
<!--            <artifactId>framework-redis-starter</artifactId>-->
<!--        </dependency>-->

        <!--存储-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-storage</artifactId>
        </dependency>

        <!--api文档-->
        <dependency>
            <groupId>com.lc</groupId>
            <artifactId>framework-apidoc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>


</project>